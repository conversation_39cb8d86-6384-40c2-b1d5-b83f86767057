from uuid import uuid4

from sqlalchemy import Integer  # type: ignore
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class TenantConfiguration(TenantBase, DateTimeMixin):
    __tablename__ = "tenant_configuration"

    tenant_uuid = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    tenant_name = Column(String, nullable=False)
    tenant_slug = Column(String, nullable=False)
    plan_key_id = Column(Integer, nullable=False)
    config = Column(JSON, nullable=True)
    default_storage = Column(Integer, nullable=True)
    extra_storage = Column(Integer, nullable=True)
