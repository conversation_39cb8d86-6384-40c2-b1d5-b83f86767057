"""Update plan_key_id at tenant_configuration model

Revision ID: 1f9b2fd538d8
Revises: 179ee6dbe786
Create Date: 2025-08-27 12:00:44.824597

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1f9b2fd538d8"  # pragma: allowlist secret
down_revision: Union[str, None] = "179ee6dbe786"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "doctor_roles",
        "role_key_id",
        existing_type=sa.INTEGER(),
        comment="Reference to role",
        existing_comment="Reference to roles.role_key_id",
        existing_nullable=False,
    )
    op.alter_column(
        "role_permissions",
        "role_key_id",
        existing_type=sa.INTEGER(),
        comment="Reference to role",
        existing_comment="Reference to roles.role_key_id",
        existing_nullable=False,
    )
    op.alter_column("tenant_configuration", "plan_id", new_column_name="plan_key_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("tenant_configuration", "plan_key_id", new_column_name="plan_id")
    op.alter_column(
        "role_permissions",
        "role_key_id",
        existing_type=sa.INTEGER(),
        comment="Reference to roles.role_key_id",
        existing_comment="Reference to role",
        existing_nullable=False,
    )
    op.alter_column(
        "doctor_roles",
        "role_key_id",
        existing_type=sa.INTEGER(),
        comment="Reference to roles.role_key_id",
        existing_comment="Reference to role",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
