from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

ERROR_PROCESS = "Request could not be processed, please try again."
INIT_CLINIC_INPROCESS = "Init clinic in process."


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Server Errors
    UNKNOWN_ERROR = 5000, "Unknown error!", "An unexpected error occurred."
    TWILIO_ERROR = (
        5001,
        "Twilio error!",
        "An error occurred while processing the Twilio request.",
    )
    TWILIO_SEND_MESSAGE_ERROR = (
        5002,
        "Twilio send message error!",
        "An error occurred while sending a message via Twilio.",
    )
    S3_BUCKET_ERROR = (
        5003,
        "S3 bucket error!",
        "An error occurred while accessing the S3 bucket.",
    )

    # Tenant Clinic
    X_TENANT_SLUG_IS_REQUIRED = (
        4000,
        "X-Tenant-Slug header is required!",
        "The X-Tenant-Slug header must be provided in the request.",
    )
    TENANT_NOT_FOUND = (
        4004,
        "Tenant not found!",
        "The specified tenant does not exist.",
    )

    CLINIC_INFO_NOT_FOUND = (
        4005,
        "Clinic information not found!",
        "The specified clinic information does not exist.",
    )
    CLINIC_CREATED_SUCCESS = (
        4006,
        "Clinic created successfully!",
        "The clinic has been created successfully.",
    )
    CLINIC_CREATED_FAILED = (
        4007,
        "Clinic creation failed!",
        "An error occurred while creating the clinic.",
    )
    CLINIC_NOT_FOUND = (
        4008,
        "Clinic not found!",
        "The specified clinic does not exist.",
    )

    # Validation Errors
    VALUE_ERROR_INVALID_DATE_PAST = (
        10000,
        "Invalid date provided!",
        "The date provided must be in the past.",
    )
    VALUE_ERROR_INVALID_END_DATE = (
        10001,
        "Invalid end date!",
        "The end date must be after the start date.",
    )

    # Master Data
    MASTER_DATA_NOT_FOUND = (
        30001,
        "Master data not found!",
        "The requested master data does not exist.",
    )
    MASTER_DATA_FETCH_SUCCESS = (
        30002,
        "Master data fetched successfully!",
        "The master data has been retrieved successfully.",
    )
    MASTER_DATA_FETCH_FAILED = (
        30003,
        "Master data fetch failed!",
        "An error occurred while fetching the master data.",
    )
    MASTER_DATA_COLUMN_NOT_EXIST = (
        30004,
        "Master data column does not exist!",
        "The specified column does not exist in the master data model.",
    )
    MASTER_DATA_INVALID_FILTER = (
        30005,
        "Invalid filter condition!",
        "The provided filter condition is invalid or malformed.",
    )

    # Source file
    IMPORT_FILE_UPLOAD_FAILED = (
        6000,
        "File upload failed!",
        "The file could not be processed.",
    )
    IMPORT_FILE_INVALID_FORMAT = (
        6001,
        "Invalid file format or content file error!",
        "The file content is not valid JSON or does not match the required data structure.",
    )
    IMPORT_FILE_UNSUPPORTED_SOURCE = (
        6002,
        "Unsupported source!",
        "The specified source is not supported by this endpoint.",
    )
    IMPORT_FILE_INVALID_DEVICE_TYPE = (
        6003,
        "Invalid device type!",
        "The 'type' field in 'fi_data' is missing or unsupported.",
    )
    IMPORT_FILE_MISSING_DATA_ID = (
        6004,
        "Missing data ID!",
        "The 'id' field in 'fi_data' is required.",
    )
    IMPORT_FILE_UPLOAD_SUCCESS = (
        6005,
        "File upload successfully",
        "File upload successfully",
    )
    IMPORT_FILE_INVALID_IMAGE_TYPE = (
        6006,
        "Invalid image file type!",
        "The uploaded image file has an unsupported format.",
    )
    IMPORT_FILE_MISSING_BUSINESS_NUMBER = (
        6007,
        "Missing business number!",
        "The required 'business_number' field is missing from the JSON payload.",
    )

    # Process file
    PROCESS_FILE_ALREADY_PROCESSED = (
        7001,
        "File already processed!",
        "This file has already been submitted and is being processed or has been completed.",
    )
    PROCESS_FILE_NO_CONFIG_FOUND = (
        7002,
        "Device configuration not found!",
        "No processing configuration was found for the specified device type.",
    )
    PROCESS_FILE_TENANT_NOT_FOUND = (
        7003,
        "Tenant not found!",
        "The tenant specified by the business number could not be found.",
    )
    PROCESS_FILE_TENANT_DB_URI_MISSING = (
        7004,
        "Tenant DB URI is missing!",
        "The database connection URI is not configured for this tenant.",
    )
    PROCESS_FILE_MISSING_BUSINESS_NUMBER = (
        7005,
        "Missing business number!",
        "The 'business_number' field is required for tenant data synchronization.",
    )
    PROCESS_FILE_SUCCESS = (
        7006,
        "Sync medical file successfully",
        "Sync medical file successfully",
    )
    PROCESS_FILE_FAILED = (
        7007,
        "Sync medical file failed!",
        "Sync medical file failed!",
    )
    PROCESS_FILE_PATIENT_USER_NOT_FOUND = (
        7008,
        "Patient not found!",
        "The specified patient does not exist.",
    )

    THUMBNAIL_CREATION_SUCCESS = (
        8000,
        "Thumbnail creation accepted",
        "The thumbnail generation task has been successfully initiated.",
    )
    THUMBNAIL_CREATION_FAILED = (
        8001,
        "Thumbnail creation failed",
        "An unexpected error occurred during the thumbnail creation process.",
    )

    # Pricing
    PRICING_STORAGE_NOT_FOUND = (
        9001,
        "Pricing storage not found!",
        "The specified pricing storage does not exist.",
    )
    PRICING_STORAGE_TENANT_NOT_FOUND = (
        9002,
        "Tenant not found!",
        "The specified tenant does not exist.",
    )
    PRICING_STORAGE_CREATED_SUCCESS = (
        9003,
        "Pricing storage created successfully!",
        "The pricing storage has been created successfully.",
    )
    PRICING_STORAGE_CREATED_FAILED = (
        9004,
        "Pricing storage creation failed!",
        "An error occurred while creating the pricing storage.",
    )
    PRICING_STORAGE_GET_LIST_FAILED = (
        9005,
        "Get list pricing storage failed!",
        "An error occurred while get list the pricing storage.",
    )
    PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND = (
        9006,
        "Tenant configuration not found!",
        "The tenant configuration does not exist.",
    )
    PRICING_STORAGE_PERIOD_NOT_FOUND = (
        9007,
        "Pricing storage period not found!",
        "The specified pricing storage period does not exist.",
    )
    PRICING_PLAN_NOT_FOUND = (
        9008,
        "Plan not found!",
        "The specified plan does not exist.",
    )
    PRICING_TENANT_PLAN_NOT_FOUND = (
        9009,
        "Tenant plan not found!",
        "The specified tenant plan does not exist.",
    )
    PRICING_PLAN_NOT_CHANGED = (
        9010,
        "Plan not changed!",
        "The specified plan is the same as the current plan.",
    )
    PRICING_PLAN_STORAGE_UPDATED_SUCCESS = (
        9011,
        "Plan storage updated successfully!",
        "The plan storage has been updated successfully.",
    )
    PRICING_PLAN_STORAGE_UPDATED_FAILED = (
        9012,
        "Plan storage update failed!",
        "An error occurred while updating the plan storage.",
    )
    PRICING_STORAGE_KEY_ID_IS_INVALID = (
        9013,
        "Pricing storage key id is invalid!",
        "The specified pricing storage key id is invalid.",
    )
    PRICING_PLAN_KEY_ID_IS_INVALID = (
        9014,
        "Plan key id is invalid!",
        "The specified plan key id is invalid.",
    )
