from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

INVALID_AUTHORIZATION_CODE = "Invalid authorization code"
INVALID_TOKEN = "Invalid token or token expired."

CLIENT_NOT_FOUND = "Client is not found."
ERROR_PROCESS = "Request could not be processed, please try again."
TENANT_NOT_FOUND = "Tenant not found!"
INVALID_USERNAME_OR_PASSWORD = (
    "The provided username or password incorrect."  # pragma: allowlist secret
)
X_TENANT_SLUG_IS_REQUIRED = "The X-Tenant-Slug header must be provided in the request."
INVALID_TOKEN = "Invalid token."
REVOKED_TOKEN_SUCCESS = "Revoked token successfully."
REVOKED_TOKEN_ERROR = "Failed to revoke token."
INVALID_REFRRESH_TOKEN = "Invalid refresh token or token is expired."
INVALID_CLIENT = "Invalid client."
INVALID_TENANT = "Invalid tenant."
INVALID_USER = "User inactive or not found."
INVALID_TOKEN_IS_REVOKED = "The token is revoked."
USER_NOT_FOUND = "User not found."
INVALID_REDIRECT_URL = "Invalid client or redirect URI."
ERROR_PROCESSING = "Request could not be processed, please try again."
INVALID_SCOPES = "Invalid scopes."
UNSUPPORT_RESPONSE_TYPE = "Unsupported response  type."
INVALID_AUTHORIZATION_CODE = "Invalid or expired authorization code"


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Server errors
    UNKNOWN_ERROR = (
        5000,
        _("Unknown error!", "「不明なエラーです!"),
        "An unexpected error occurred.",
    )
    MISSING_JSON_IN_REQUEST = (
        5001,
        _("Missing JSON in request", "リクエストにJSONがありません"),
        "The request is missing a JSON payload.",
    )
    TENANT_NOT_FOUND = (
        5002,
        _("Tenant not found", "テナントが見つかりません"),
        "The specified tenant does not exist.",
    )
    INVALID_REFRRESH_TOKEN = (
        5003,
        _(
            "Invalid refresh token or token is expired.",
            "無効なリフレッシュトークンまたはトークンが期限切れです",
        ),
        "The provided refresh token is invalid or expired.",
    )
    INVALID_GRANT_TYPE = (
        5004,
        _("Invalid grant type", "無効なグラントタイプ"),
        "The provided grant type is not supported or invalid.",
    )
    TOKEN_SIGNATURE_VERIFICATION_FAILED = (
        5005,
        _("Token signature verification failed", "トークンの署名検証に失敗しました"),
        "The signature of the provided token could not be verified.",
    )

    # Invalid
    INVALID_REQUEST_DATA = (
        4000,
        _("Invalid request", "無効なリクエスト"),
        "The request is invalid or malformed.",
    )
    INVALID_PHONE_NUMBER = (
        4001,
        _("Invalid phone number", "無効な電話番号"),
        "The provided phone number is invalid.",
    )
    INVALID_USER = (
        4002,
        _("User inactive or not found.", "ユーザーが非アクティブまたは見つかりません"),
        "The user is inactive or does not exist.",
    )
    INTERNAL_CLIENT_NOT_FOUND = (
        4003,
        _("Internal client not found", "内部クライアントが見つかりません"),
        "The internal client is not found in the system.",
    )
    X_TENANT_UUID_IS_REQUIRED = (
        4004,
        "X-Tenant-UUID header is required!",
        "The X-UUID-Slug header must be provided in the request.",
    )
    X_TENANT_NOT_FOUND = (
        4005,
        "Tenant not found!",
        "The specified tenant does not exist.",
    )
    MAIL_TEMPLATE_NOT_FOUND = (
        4006,
        "Mail template not found!",
        "The specified mail template does not exist.",
    )
    INVALID_CONDITION = (
        4007,
        _("Invalid condition", "無効な条件"),
        "The provided condition is invalid or not met.",
    )
    X_TENANT_SLUG_IS_REQUIRED = (
        4008,
        "X-Tenant-Slug header is required!",
        "The X-Tenant-Slug header must be provided in the request.",
    )
    INVALID_AUTHORIZATION_HEADER = (
        4009,
        "Invalid authorization header!",
        "The Authorization header is invalid or missing.",
    )
    INVALID_TOKEN = (
        4010,
        _("Invalid token", "無効なトークン"),
        "The provided token is invalid or expired.",
    )

    # Authz
    USER_NOT_FOUND = (
        6000,
        _("User not found", "ユーザーが見つかりません"),
        "The user does not exist or is inactive.",
    )
    OTP_SENT_SUCCESS = (
        6001,
        _(
            "OTP sent successfully. Please check your phone.",
            "OTPが正常に送信されました。電話を確認してください",
        ),
        "The OTP has been sent successfully to the user's phone.",
    )
    OTP_VERIFICATION_FAILED = (
        6002,
        _("OTP verification failed", "OTP検証に失敗しました"),
        "The OTP verification failed. Please try again.",
    )
    INVALID_USERNAME_OR_PASSWORD = (
        6003,
        _("Invalid username or password", "ユーザー名またはパスワードが無効です"),
        "The provided username or password is incorrect.",
    )
    SEND_MAIL_OTP_FAILED = (
        6004,
        "Failed to send mail OTP!",
        "Failed to send mail OTP to the doctor's email address.",
    )
    SEND_MAIL_OTP_SUCCESS = (
        6005,
        "OTP mail sent successfully!",
        "OTP mail sent successfully to the doctor's email address.",
    )
    OTP_INCORRECT = (
        6006,
        "Incorrect OTP",
        "The provided OTP is incorrect.",
    )
    DOCTOR_USER_NOT_FOUND = (
        6007,
        "Doctor user not found!",
        "The specified doctor user does not exist.",
    )
    TENANT_USER_MAPPING_NOT_FOUND = (
        6008,
        "Tenant user mapping not found!",
        "The tenant user mapping for the specified doctor does not exist.",
    )
    OTP_NOT_FOUND_OR_EXPIRED = (
        6009,
        "OTP not found or expired!",
        "The OTP is not found or has expired.",
    )
    OTP_TEMPORARILY_LOCKED = (
        6010,
        "OTP temporarily locked",
        "OTP has been locked due to many send attempts.",
    )
    DOCTOR_ROLE_NOT_FOUND = (
        6011,
        "Doctor role not found!",
        "The specified doctor role does not exist.",
    )

    # OAuth Client
    INVALID_SCOPES = (
        7000,
        _("Invalid scopes", "無効なスコープ"),
        "The provided scopes are invalid or not supported.",
    )
    INVALID_CLIENT_NAME = (
        7001,
        _("Invalid client name", "無効なクライアント名"),
        "The provided client name is invalid or empty.",
    )
